{% comment %}
Material Design M2 Reusable Modal Component
Compatible with HTMX and Django forms

Usage:
{% include 'material/modal.html' with modal_id='student-modal' modal_title='Add Student' %}

Parameters:
- modal_id: Unique ID for the modal (required)
- modal_title: Title displayed in modal header (required)
- modal_size: 'small', 'medium', 'large', 'xl' (default: 'medium')
- modal_form_id: ID for the form inside modal (default: modal_id + '-form')
- submit_text: Text for submit button (default: 'Save')
- cancel_text: Text for cancel button (default: 'Cancel')
- show_footer: Whether to show modal footer with buttons (default: true)
{% endcomment %}

{% load static %}

<!-- Modal Overlay -->
<div class="modal-overlay" id="{{ modal_id }}-overlay">
    <div class="modal {% if modal_size == 'small' %}modal--small{% elif modal_size == 'large' %}modal--large{% elif modal_size == 'xl' %}modal--xl{% endif %}" 
         id="{{ modal_id }}">
        
        <!-- Modal Header -->
        <div class="modal-header">
            <h2 class="modal-title" id="{{ modal_id }}-title">{{ modal_title|default:"Modal" }}</h2>
            <button class="modal-close" id="{{ modal_id }}-close" type="button">
                <span class="material-icons">close</span>
            </button>
        </div>
        
        <!-- Modal Content -->
        <div class="modal-content" id="{{ modal_id }}-content">
            <!-- Content will be loaded here via HTMX or populated directly -->
        </div>
        
        <!-- Modal Footer -->
        {% if show_footer|default:true %}
        <div class="modal-actions" id="{{ modal_id }}-actions">
            <button type="button" class="mdc-button mdc-button--outlined modal-cancel" id="{{ modal_id }}-cancel">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">{{ cancel_text|default:"Annuler" }}</span>
            </button>
            <button type="submit" class="mdc-button mdc-button--raised modal-submit" id="{{ modal_id }}-submit" 
                    form="{{ modal_form_id|default:modal_id }}-form">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">{{ submit_text|default:"Enregistrer" }}</span>
            </button>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Modal functionality for {{ modal_id }}
(function() {
    const modalId = '{{ modal_id }}';
    const modalOverlay = document.getElementById(modalId + '-overlay');
    const modal = document.getElementById(modalId);
    const modalContent = document.getElementById(modalId + '-content');
    const modalTitle = document.getElementById(modalId + '-title');
    const modalClose = document.getElementById(modalId + '-close');
    const modalCancel = document.getElementById(modalId + '-cancel');
    const modalSubmit = document.getElementById(modalId + '-submit');
    
    // Initialize MDC components for modal buttons
    function initializeModalButtons() {
        if (typeof mdc !== 'undefined' && mdc.ripple) {
            if (modalCancel && !modalCancel.mdcRipple) {
                modalCancel.mdcRipple = new mdc.ripple.MDCRipple(modalCancel);
            }
            if (modalSubmit && !modalSubmit.mdcRipple) {
                modalSubmit.mdcRipple = new mdc.ripple.MDCRipple(modalSubmit);
            }
        }
    }
    
    // Initialize form components within modal
    function initializeModalFormComponents() {
        if (!modalContent) return;
        
        // Initialize MDC text fields
        modalContent.querySelectorAll('.mdc-text-field').forEach(textField => {
            if (typeof mdc !== 'undefined' && mdc.textField && !textField.mdcTextField) {
                textField.mdcTextField = new mdc.textField.MDCTextField(textField);
            }
        });
        
        // Initialize MDC select fields
        modalContent.querySelectorAll('.mdc-select').forEach(select => {
            if (typeof mdc !== 'undefined' && mdc.select && !select.mdcSelect) {
                select.mdcSelect = new mdc.select.MDCSelect(select);
            }
        });
        
        // Initialize MDC checkboxes
        modalContent.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
            if (typeof mdc !== 'undefined' && mdc.checkbox && !checkbox.mdcCheckbox) {
                checkbox.mdcCheckbox = new mdc.checkbox.MDCCheckbox(checkbox);
            }
        });
        
        // Initialize MDC radio buttons
        modalContent.querySelectorAll('.mdc-radio').forEach(radio => {
            if (typeof mdc !== 'undefined' && mdc.radio && !radio.mdcRadio) {
                radio.mdcRadio = new mdc.radio.MDCRadio(radio);
            }
        });
        
        // Initialize MDC switches
        modalContent.querySelectorAll('.mdc-switch').forEach(switchEl => {
            if (typeof mdc !== 'undefined' && mdc.switchControl && !switchEl.mdcSwitch) {
                switchEl.mdcSwitch = new mdc.switchControl.MDCSwitch(switchEl);
            }
        });
    }
    
    // Show modal
    function showModal() {
        if (modalOverlay) {
            modalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Focus first input after animation
            setTimeout(() => {
                const firstInput = modalContent.querySelector('input, select, textarea');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 300);
        }
    }
    
    // Hide modal
    function hideModal() {
        if (modalOverlay) {
            modalOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
    
    // Event listeners
    if (modalClose) {
        modalClose.addEventListener('click', hideModal);
    }
    
    if (modalCancel) {
        modalCancel.addEventListener('click', hideModal);
    }
    
    if (modalOverlay) {
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                hideModal();
            }
        });
    }
    
    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modalOverlay && modalOverlay.classList.contains('active')) {
            hideModal();
        }
    });
    
    // Initialize components when modal content changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Small delay to ensure DOM is ready
                setTimeout(() => {
                    initializeModalFormComponents();
                }, 50);
            }
        });
    });
    
    if (modalContent) {
        observer.observe(modalContent, { childList: true, subtree: true });
    }
    
    // Initialize on load
    document.addEventListener('DOMContentLoaded', () => {
        initializeModalButtons();
        initializeModalFormComponents();
    });
    
    // Reinitialize after HTMX requests
    document.addEventListener('htmx:afterSwap', (event) => {
        if (event.detail.target && 
            (event.detail.target.id === modalId + '-content' || 
             event.detail.target.closest('#' + modalId + '-content'))) {
            setTimeout(() => {
                initializeModalFormComponents();
            }, 50);
        }
    });
    
    // Expose functions globally for external use
    window[modalId + 'Modal'] = {
        show: showModal,
        hide: hideModal,
        setTitle: (title) => {
            if (modalTitle) modalTitle.textContent = title;
        },
        setContent: (content) => {
            if (modalContent) {
                modalContent.innerHTML = content;
                initializeModalFormComponents();
            }
        }
    };
})();
</script>
