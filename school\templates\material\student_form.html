{% load i18n %}
{% load widget_tweaks %}

<form id="student-form" method="post" enctype="multipart/form-data"
      hx-post="{{ request.path }}"
      hx-target="#student-modal-content"
      hx-swap="innerHTML">
    {% csrf_token %}
    
    <div class="student-form-columns">
        <!-- Column 1: Student Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">person</span>
                Informations sur l'élève
            </div>
            
            <!-- Student ID -->
            <div class="form-field">
                <div class="mdc-text-field mdc-text-field--outlined">
                    <input type="text" class="mdc-text-field__input" 
                           id="{{ student_form.student_id.id_for_label }}"
                           name="{{ student_form.student_id.name }}"
                           value="{{ student_form.student_id.value|default:'' }}"
                           onkeyup="this.value = this.value.toUpperCase();">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label for="{{ student_form.student_id.id_for_label }}" class="mdc-floating-label">
                                {{ student_form.student_id.label }}
                            </label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
            </div>
            
            <!-- Name Fields -->
            <div class="form-row">
                <div class="form-field" style="flex: 0 0 35%;">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="text" class="mdc-text-field__input" 
                               id="{{ student_form.last_name.id_for_label }}"
                               name="{{ student_form.last_name.name }}"
                               value="{{ student_form.last_name.value|default:'' }}"
                               onkeyup="this.value = this.value.toUpperCase();"
                               required>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ student_form.last_name.id_for_label }}" class="mdc-floating-label">
                                    *{{ student_form.last_name.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="form-field" style="flex: 1;">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="text" class="mdc-text-field__input" 
                               id="{{ student_form.first_name.id_for_label }}"
                               name="{{ student_form.first_name.name }}"
                               value="{{ student_form.first_name.value|default:'' }}"
                               onkeyup="this.value = this.value.toUpperCase();"
                               {% if user.school.education != 'F' %}
                               hx-get="/transliterate/?field=full_name_ar" 
                               hx-target="#{{ student_form.full_name_ar.id_for_label }}" 
                               hx-trigger="keyup delay:3s" 
                               hx-swap="outerHTML"
                               {% endif %}
                               required>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ student_form.first_name.id_for_label }}" class="mdc-floating-label">
                                    *{{ student_form.first_name.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Birth Date Fields -->
            <div class="form-row">
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">{{ student_form.birth_day.value|default:'' }}</span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">{{ student_form.birth_day.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in student_form.birth_day.field.choices %}
                                <li class="mdc-deprecated-list-item {% if choice.0 == student_form.birth_day.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.0 }}">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.1 }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ student_form.birth_day.name }}" style="display: none;">
                            {% for choice in student_form.birth_day.field.choices %}
                            <option value="{{ choice.0 }}" {% if choice.0 == student_form.birth_day.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">{{ student_form.birth_month.value|default:'' }}</span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">{{ student_form.birth_month.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in student_form.birth_month.field.choices %}
                                <li class="mdc-deprecated-list-item {% if choice.0 == student_form.birth_month.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.0 }}">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.1 }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ student_form.birth_month.name }}" style="display: none;">
                            {% for choice in student_form.birth_month.field.choices %}
                            <option value="{{ choice.0 }}" {% if choice.0 == student_form.birth_month.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="number" class="mdc-text-field__input" 
                               id="{{ student_form.birth_year.id_for_label }}"
                               name="{{ student_form.birth_year.name }}"
                               value="{{ student_form.birth_year.value|default:'' }}"
                               max="{{ max_year }}">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ student_form.birth_year.id_for_label }}" class="mdc-floating-label">
                                    {{ student_form.birth_year.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Birth Place, Gender, Nationality -->
            <div class="form-row">
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="text" class="mdc-text-field__input" 
                               id="{{ student_form.birth_place.id_for_label }}"
                               name="{{ student_form.birth_place.name }}"
                               value="{{ student_form.birth_place.value|default:'' }}"
                               onkeyup="this.value = this.value.toUpperCase();"
                               {% if user.school.education != 'F' %}
                               hx-get="/transliterate/?field=birth_place_ar" 
                               hx-target="#{{ student_form.birth_place_ar.id_for_label }}" 
                               hx-trigger="keyup delay:3s" 
                               hx-swap="outerHTML"
                               {% endif %}>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ student_form.birth_place.id_for_label }}" class="mdc-floating-label">
                                    {{ student_form.birth_place.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% for choice in student_form.gender.field.choices %}
                                        {% if choice.0 == student_form.gender.value %}{{ choice.1 }}{% endif %}
                                    {% endfor %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">*{{ student_form.gender.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in student_form.gender.field.choices %}
                                <li class="mdc-deprecated-list-item {% if choice.0 == student_form.gender.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.0 }}">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.1 }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ student_form.gender.name }}" style="display: none;" required>
                            {% for choice in student_form.gender.field.choices %}
                            <option value="{{ choice.0 }}" {% if choice.0 == student_form.gender.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% for choice in student_form.nationality.field.choices %}
                                        {% if choice.0 == student_form.nationality.value %}{{ choice.1 }}{% endif %}
                                    {% endfor %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">*{{ student_form.nationality.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in student_form.nationality.field.choices %}
                                <li class="mdc-deprecated-list-item {% if choice.0 == student_form.nationality.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.0 }}">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.1 }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ student_form.nationality.name }}" style="display: none;" required>
                            {% for choice in student_form.nationality.field.choices %}
                            <option value="{{ choice.0 }}" {% if choice.0 == student_form.nationality.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Arabic Name Fields (if not French education) -->
            {% if user.school.education != 'F' %}
            <div class="form-row">
                <div class="form-field" style="flex: 2;">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="text" class="mdc-text-field__input"
                               id="{{ student_form.full_name_ar.id_for_label }}"
                               name="{{ student_form.full_name_ar.name }}"
                               value="{{ student_form.full_name_ar.value|default:'' }}">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ student_form.full_name_ar.id_for_label }}" class="mdc-floating-label">
                                    {{ student_form.full_name_ar.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="form-field" style="flex: 1;">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="text" class="mdc-text-field__input"
                               id="{{ student_form.birth_place_ar.id_for_label }}"
                               name="{{ student_form.birth_place_ar.name }}"
                               value="{{ student_form.birth_place_ar.value|default:'' }}">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ student_form.birth_place_ar.id_for_label }}" class="mdc-floating-label">
                                    Lieu Naiss. Arabe
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Column 2: Parent Information and Photo -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">family_restroom</span>
                Infos parents et Photo
            </div>
            
            <!-- Father -->
            <div class="form-field">
                <div class="mdc-text-field mdc-text-field--outlined">
                    <input type="text" class="mdc-text-field__input" 
                           id="{{ parents_form.father.id_for_label }}"
                           name="{{ parents_form.father.name }}"
                           value="{{ parents_form.father.value|default:'' }}"
                           onkeyup="this.value = this.value.toUpperCase();">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label for="{{ parents_form.father.id_for_label }}" class="mdc-floating-label">
                                {{ parents_form.father.label }}
                            </label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
            </div>
            
            <!-- Mother -->
            <div class="form-field">
                <div class="mdc-text-field mdc-text-field--outlined">
                    <input type="text" class="mdc-text-field__input" 
                           id="{{ parents_form.mother.id_for_label }}"
                           name="{{ parents_form.mother.name }}"
                           value="{{ parents_form.mother.value|default:'' }}"
                           onkeyup="this.value = this.value.toUpperCase();">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label for="{{ parents_form.mother.id_for_label }}" class="mdc-floating-label">
                                {{ parents_form.mother.label }}
                            </label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
            </div>
            
            <!-- Parent Phone -->
            <div class="form-field">
                <div class="mdc-text-field mdc-text-field--outlined">
                    <input type="tel" class="mdc-text-field__input" 
                           id="{{ parents_form.father_phone.id_for_label }}"
                           name="{{ parents_form.father_phone.name }}"
                           value="{{ parents_form.father_phone.value|default:'' }}">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch">
                            <label for="{{ parents_form.father_phone.id_for_label }}" class="mdc-floating-label">
                                Contact du parent ou du tuteur
                            </label>
                        </div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
            </div>
            
            <!-- Photo Upload Section -->
            <div class="photo-upload-section">
                {% if enrollment and enrollment.student.photo %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ enrollment.student.photo.url }}" id="photo-preview">
                {% else %}
                    <img alt="Photo de l'élève" class="photo-preview" 
                         src="{{ blank_photo }}" id="photo-preview">
                {% endif %}
                
                <input type="file" class="mdc-text-field__input" 
                       name="3-photo" id="{{ files_form.photo.id_for_label }}"
                       accept=".jpg, .png, .jpeg" 
                       onchange="previewPhoto(this)"
                       style="display: none;">
                
                <button type="button" class="mdc-button mdc-button--outlined" 
                        onclick="document.getElementById('{{ files_form.photo.id_for_label }}').click()">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">photo_camera</span>
                    <span class="mdc-button__label">Choisir une photo</span>
                </button>
            </div>
        </div>
        
        <!-- Column 3: Registration Information -->
        <div class="student-form-column">
            <div class="form-section-title">
                <span class="material-icons">school</span>
                Fréquentation {{ active_year }}
            </div>

            <!-- Subschool (if multiple) -->
            {% if user.school.subschool_set.count > 1 %}
            <div class="form-field">
                <div class="mdc-select mdc-select--outlined">
                    <div class="mdc-select__anchor">
                        <span class="mdc-select__selected-text-container">
                            <span class="mdc-select__selected-text">
                                {% for choice in level_form.subschool.field.queryset %}
                                    {% if choice.id == level_form.subschool.value %}{{ choice }}{% endif %}
                                {% endfor %}
                            </span>
                        </span>
                        <span class="mdc-select__dropdown-icon">
                            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                            </svg>
                        </span>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label class="mdc-floating-label">Ecole</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                    <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                        <ul class="mdc-deprecated-list">
                            {% for choice in level_form.subschool.field.queryset %}
                            <li class="mdc-deprecated-list-item {% if choice.id == level_form.subschool.value %}mdc-deprecated-list-item--selected{% endif %}"
                                data-value="{{ choice.id }}">
                                <span class="mdc-deprecated-list-item__text">{{ choice }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    <select name="{{ level_form.subschool.name }}" style="display: none;">
                        {% for choice in level_form.subschool.field.queryset %}
                        <option value="{{ choice.id }}" {% if choice.id == level_form.subschool.value %}selected{% endif %}>{{ choice }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            {% endif %}

            <!-- Level Fields -->
            <div class="form-row">
                {% if level_form.level_fr.help_text != 'd-none' %}
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% for choice in level_form.level_fr.field.queryset %}
                                        {% if choice.id == level_form.level_fr.value %}{{ choice.number }}{% endif %}
                                    {% endfor %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">*{{ level_form.level_fr.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in level_form.level_fr.field.queryset %}
                                <li class="mdc-deprecated-list-item {% if choice.id == level_form.level_fr.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.id }}"
                                    hx-get="/versements/frais_scolarite/"
                                    hx-target="#id_2-year_fees"
                                    hx-swap="outerHTML">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.number }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ level_form.level_fr.name }}" style="display: none;" required>
                            {% for choice in level_form.level_fr.field.queryset %}
                            <option value="{{ choice.id }}" {% if choice.id == level_form.level_fr.value %}selected{% endif %}>{{ choice.number }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                {% endif %}

                {% if level_form.level_ar.help_text != 'd-none' %}
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% for choice in level_form.level_ar.field.queryset %}
                                        {% if choice.id == level_form.level_ar.value %}{{ choice.number }}{% endif %}
                                    {% endfor %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">{{ level_form.level_ar.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in level_form.level_ar.field.queryset %}
                                <li class="mdc-deprecated-list-item {% if choice.id == level_form.level_ar.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.id }}"
                                    hx-get="/versements/frais_scolarite/"
                                    hx-target="#id_2-year_fees"
                                    hx-swap="outerHTML">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.number }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ level_form.level_ar.name }}" style="display: none;">
                            {% for choice in level_form.level_ar.field.queryset %}
                            <option value="{{ choice.id }}" {% if choice.id == level_form.level_ar.value %}selected{% endif %}>{{ choice.number }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Quality and Status -->
            <div class="form-row">
                <div class="form-field" style="flex: 2;">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% for choice in level_form.qualite.field.choices %}
                                        {% if choice.0 == level_form.qualite.value %}{{ choice.1 }}{% endif %}
                                    {% endfor %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">{{ level_form.qualite.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in level_form.qualite.field.choices %}
                                <li class="mdc-deprecated-list-item {% if choice.0 == level_form.qualite.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.0 }}">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.1 }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ level_form.qualite.name }}" style="display: none;">
                            {% for choice in level_form.qualite.field.choices %}
                            <option value="{{ choice.0 }}" {% if choice.0 == level_form.qualite.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-field" style="flex: 1;">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% for choice in level_form.status.field.choices %}
                                        {% if choice.0 == level_form.status.value %}{{ choice.1 }}{% endif %}
                                    {% endfor %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">{{ level_form.status.label }}</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                {% for choice in level_form.status.field.choices %}
                                <li class="mdc-deprecated-list-item {% if choice.0 == level_form.status.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.0 }}"
                                    hx-get="/versements/frais_scolarite/"
                                    hx-target="#id_2-year_fees"
                                    hx-swap="outerHTML">
                                    <span class="mdc-deprecated-list-item__text">{{ choice.1 }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <select name="{{ level_form.status.name }}" style="display: none;">
                            {% for choice in level_form.status.field.choices %}
                            <option value="{{ choice.0 }}" {% if choice.0 == level_form.status.value %}selected{% endif %}>{{ choice.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Fees Section -->
            {% if perms.school.add_payment %}
            <div class="form-section-title" style="margin-top: 24px;">
                <span class="material-icons">payments</span>
                Montant à payer par rubrique
            </div>

            <div class="form-row">
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="number" class="mdc-text-field__input"
                               id="{{ level_form.enrollment_fees.id_for_label }}"
                               name="{{ level_form.enrollment_fees.name }}"
                               value="{{ level_form.enrollment_fees.value|default:0 }}"
                               min="0">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ level_form.enrollment_fees.id_for_label }}" class="mdc-floating-label">
                                    {{ level_form.enrollment_fees.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="number" class="mdc-text-field__input"
                               id="{{ level_form.year_fees.id_for_label }}"
                               name="{{ level_form.year_fees.name }}"
                               value="{{ level_form.year_fees.value|default:0 }}"
                               min="0">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ level_form.year_fees.id_for_label }}" class="mdc-floating-label">
                                    {{ level_form.year_fees.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input type="number" class="mdc-text-field__input"
                               id="{{ level_form.annexe_fees.id_for_label }}"
                               name="{{ level_form.annexe_fees.name }}"
                               value="{{ level_form.annexe_fees.value|default:0 }}"
                               min="0">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label for="{{ level_form.annexe_fees.id_for_label }}" class="mdc-floating-label">
                                    {{ level_form.annexe_fees.label }}
                                </label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</form>

<script>
function previewPhoto(input) {
    const preview = document.getElementById('photo-preview');
    const reader = new FileReader();
    
    if (input.files && input.files[0]) {
        reader.onload = function(e) {
            preview.src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.src = '{{ blank_photo }}';
    }
}
</script>
